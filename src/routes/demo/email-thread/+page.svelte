<script lang="ts">
	import EmailThread from '$lib/components/conversation/EmailThread.svelte';
	import { onMount } from 'svelte';

	let loading = false;
	let threads = [];

	// Handle thread toggle events
	function handleThreadToggle(event) {
		console.log('Thread toggled:', event.detail);
	}

	// Handle email click events
	function handleEmailClick(event) {
		console.log('Email clicked:', event.detail);
		// Here you could open a detailed email view, mark as read, etc.
	}

	// Simulate loading state
	function simulateLoading() {
		loading = true;
		setTimeout(() => {
			loading = false;
		}, 2000);
	}

	onMount(() => {
		// Component will use mock data by default
	});
</script>

<svelte:head>
	<title>Email Thread Demo - Salmate</title>
</svelte:head>

<div class="min-h-screen bg-gray-100">
	<!-- Header -->
	<div class="bg-white shadow-sm border-b border-gray-200">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between items-center py-4">
				<div>
					<h1 class="text-2xl font-bold text-gray-900">Email Thread Demo</h1>
					<p class="text-sm text-gray-600 mt-1">
						Interactive email thread interface component
					</p>
				</div>
				<div class="flex space-x-3">
					<button
						on:click={simulateLoading}
						class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
					>
						Simulate Loading
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Main content -->
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
			<!-- Email Thread Component -->
			<div class="lg:col-span-2">
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 h-[600px] flex flex-col">
					<div class="px-6 py-4 border-b border-gray-200">
						<h2 class="text-lg font-medium text-gray-900">Email Threads</h2>
						<p class="text-sm text-gray-600 mt-1">
							Click on threads to expand and view messages
						</p>
					</div>
					
					<EmailThread 
						{threads}
						{loading}
						on:threadToggle={handleThreadToggle}
						on:emailClick={handleEmailClick}
					/>
				</div>
			</div>

			<!-- Info Panel -->
			<div class="lg:col-span-1">
				<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					<h3 class="text-lg font-medium text-gray-900 mb-4">Component Features</h3>
					
					<div class="space-y-4">
						<div class="flex items-start space-x-3">
							<div class="flex-shrink-0 h-5 w-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
								<svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
							</div>
							<div>
								<p class="text-sm font-medium text-gray-900">Collapsible Threads</p>
								<p class="text-sm text-gray-600">Click to expand/collapse email threads</p>
							</div>
						</div>

						<div class="flex items-start space-x-3">
							<div class="flex-shrink-0 h-5 w-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
								<svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
							</div>
							<div>
								<p class="text-sm font-medium text-gray-900">Visual Indicators</p>
								<p class="text-sm text-gray-600">NEW badges, unread dots, timestamps</p>
							</div>
						</div>

						<div class="flex items-start space-x-3">
							<div class="flex-shrink-0 h-5 w-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
								<svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
							</div>
							<div>
								<p class="text-sm font-medium text-gray-900">Avatar Support</p>
								<p class="text-sm text-gray-600">Profile images with fallback initials</p>
							</div>
						</div>

						<div class="flex items-start space-x-3">
							<div class="flex-shrink-0 h-5 w-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
								<svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
							</div>
							<div>
								<p class="text-sm font-medium text-gray-900">Attachment Icons</p>
								<p class="text-sm text-gray-600">Shows attachment count when present</p>
							</div>
						</div>

						<div class="flex items-start space-x-3">
							<div class="flex-shrink-0 h-5 w-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
								<svg class="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
							</div>
							<div>
								<p class="text-sm font-medium text-gray-900">Event Handling</p>
								<p class="text-sm text-gray-600">Dispatches events for thread/email interactions</p>
							</div>
						</div>
					</div>

					<div class="mt-6 pt-6 border-t border-gray-200">
						<h4 class="text-sm font-medium text-gray-900 mb-2">Usage</h4>
						<div class="bg-gray-50 rounded-md p-3">
							<code class="text-xs text-gray-800">
								&lt;EmailThread<br/>
								&nbsp;&nbsp;{'{threads}'}<br/>
								&nbsp;&nbsp;{'{loading}'}<br/>
								&nbsp;&nbsp;on:threadToggle={'{handleToggle}'}<br/>
								&nbsp;&nbsp;on:emailClick={'{handleClick}'}<br/>
								/&gt;
							</code>
						</div>
					</div>
				</div>

				<!-- Mock Data Info -->
				<div class="mt-6 bg-blue-50 rounded-lg border border-blue-200 p-4">
					<div class="flex items-start space-x-3">
						<div class="flex-shrink-0">
							<svg class="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
							</svg>
						</div>
						<div>
							<h4 class="text-sm font-medium text-blue-900">Mock Data</h4>
							<p class="text-sm text-blue-700 mt-1">
								This demo uses mock email data. In production, pass real thread data via the <code class="bg-blue-100 px-1 rounded">threads</code> prop.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	code {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	}
</style>
