# EmailThread Component

A Svelte component for displaying email threads in a collapsible, interactive format. Designed to integrate seamlessly with the existing conversation interface styling.

## Features

- **Collapsible Threads**: Click to expand/collapse email threads
- **Visual Indicators**: NEW badges, unread dots, timestamps
- **Avatar Support**: Profile images with fallback initials
- **Attachment Icons**: Shows attachment count when present
- **Event Handling**: Dispatches events for thread/email interactions
- **Responsive Design**: Works well on different screen sizes
- **Accessibility**: Keyboard navigation and focus management

## Usage

```svelte
<script>
  import EmailThread from '$lib/components/conversation/EmailThread.svelte';
  
  let threads = []; // Your email thread data
  let loading = false;
  
  function handleThreadToggle(event) {
    console.log('Thread toggled:', event.detail);
  }
  
  function handleEmailClick(event) {
    console.log('Email clicked:', event.detail);
  }
</script>

<EmailThread 
  {threads}
  {loading}
  on:threadToggle={handleThreadToggle}
  on:emailClick={handleEmailClick}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `threads` | `EmailThread[]` | `[]` | Array of email thread objects |
| `loading` | `boolean` | `false` | Shows loading state when true |

## Events

| Event | Detail | Description |
|-------|--------|-------------|
| `threadToggle` | `{ threadId: string, isExpanded: boolean }` | Fired when a thread is expanded/collapsed |
| `emailClick` | `{ email: EmailMessage }` | Fired when an individual email is clicked |

## Data Structure

### EmailThread Interface

```typescript
interface EmailThread {
  id: string;
  subject: string;
  participants: string[];
  messageCount: number;
  lastActivity: string;
  isExpanded: boolean;
  messages: EmailMessage[];
  hasUnread: boolean;
  isNew?: boolean;
}
```

### EmailMessage Interface

```typescript
interface EmailMessage {
  id: number;
  threadId: string;
  sender: {
    name: string;
    email: string;
    avatar?: string;
  };
  subject: string;
  body: string;
  timestamp: string;
  isRead: boolean;
  isFromSelf: boolean;
  hasAttachments?: boolean;
  attachmentCount?: number;
}
```

## Styling

The component follows the existing design system:
- Uses Tailwind CSS classes
- Consistent with MessageList.svelte patterns
- Custom scrollbar styling
- Smooth transitions and hover effects
- Focus states for accessibility

## Demo

Visit `/demo/email-thread` to see the component in action with mock data.

## Integration Notes

- The component uses existing utility functions from `messageFormatter.ts`
- Avatar generation follows the same pattern as other components
- Icons are from the `flowbite-svelte-icons` library
- Follows the same event dispatching patterns as MessageList

## Mock Data

The component includes mock data for development and testing. In production, pass real email thread data via the `threads` prop.

## Future Enhancements

- Search functionality within threads
- Drag and drop for email organization
- Bulk actions (mark as read, archive, etc.)
- Email composition integration
- Real-time updates via WebSocket
- Infinite scrolling for large thread lists
