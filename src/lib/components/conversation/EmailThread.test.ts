import { render, fireEvent, screen } from '@testing-library/svelte';
import { describe, it, expect, vi } from 'vitest';
import EmailThread from './EmailThread.svelte';

// Mock data for testing
const mockThreads = [
  {
    id: 'thread-1',
    subject: 'Test Email Thread',
    participants: ['<PERSON>', '<PERSON>'],
    messageCount: 2,
    lastActivity: '2024-09-26T14:31:00Z',
    isExpanded: false,
    hasUnread: true,
    isNew: true,
    messages: [
      {
        id: 1,
        threadId: 'thread-1',
        sender: {
          name: '<PERSON>',
          email: '<EMAIL>'
        },
        subject: 'Test Email Thread',
        body: 'This is a test email message.',
        timestamp: '2024-09-26T14:30:00Z',
        isRead: true,
        isFromSelf: false
      },
      {
        id: 2,
        threadId: 'thread-1',
        sender: {
          name: '<PERSON>',
          email: '<EMAIL>'
        },
        subject: 'Re: Test Email Thread',
        body: 'This is a reply to the test email.',
        timestamp: '2024-09-26T14:31:00Z',
        isRead: false,
        isFromSelf: true
      }
    ]
  }
];

describe('EmailThread Component', () => {
  it('renders without crashing', () => {
    render(EmailThread, { props: { threads: [] } });
  });

  it('displays empty state when no threads provided', () => {
    render(EmailThread, { props: { threads: [] } });
    expect(screen.getByText('No email threads found')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    render(EmailThread, { props: { threads: [], loading: true } });
    expect(screen.getByText('Loading email threads...')).toBeInTheDocument();
  });

  it('renders thread with correct information', () => {
    render(EmailThread, { props: { threads: mockThreads } });
    
    // Check if thread subject is displayed
    expect(screen.getByText('Test Email Thread')).toBeInTheDocument();
    
    // Check if participants are displayed
    expect(screen.getByText('John Doe, Jane Smith')).toBeInTheDocument();
    
    // Check if message count is displayed
    expect(screen.getByText('2 messages')).toBeInTheDocument();
    
    // Check if NEW badge is displayed
    expect(screen.getByText('NEW')).toBeInTheDocument();
  });

  it('expands thread when clicked', async () => {
    const { component } = render(EmailThread, { props: { threads: mockThreads } });
    
    // Mock event listener
    const threadToggleSpy = vi.fn();
    component.$on('threadToggle', threadToggleSpy);
    
    // Find and click the thread header
    const threadHeader = screen.getByRole('button');
    await fireEvent.click(threadHeader);
    
    // Check if event was dispatched
    expect(threadToggleSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        detail: expect.objectContaining({
          threadId: 'thread-1',
          isExpanded: true
        })
      })
    );
  });

  it('displays messages when thread is expanded', () => {
    const expandedThreads = [
      {
        ...mockThreads[0],
        isExpanded: true
      }
    ];
    
    render(EmailThread, { props: { threads: expandedThreads } });
    
    // Check if individual messages are displayed
    expect(screen.getByText('This is a test email message.')).toBeInTheDocument();
    expect(screen.getByText('This is a reply to the test email.')).toBeInTheDocument();
  });

  it('handles email click events', async () => {
    const expandedThreads = [
      {
        ...mockThreads[0],
        isExpanded: true
      }
    ];
    
    const { component } = render(EmailThread, { props: { threads: expandedThreads } });
    
    // Mock event listener
    const emailClickSpy = vi.fn();
    component.$on('emailClick', emailClickSpy);
    
    // Find and click an email message
    const emailMessages = screen.getAllByRole('button');
    const emailMessage = emailMessages.find(button => 
      button.textContent?.includes('This is a test email message.')
    );
    
    if (emailMessage) {
      await fireEvent.click(emailMessage);
      
      // Check if event was dispatched
      expect(emailClickSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: expect.objectContaining({
            email: expect.objectContaining({
              id: 1,
              body: 'This is a test email message.'
            })
          })
        })
      );
    }
  });

  it('displays unread indicators correctly', () => {
    render(EmailThread, { props: { threads: mockThreads } });
    
    // Check for unread dot on thread level
    const unreadDots = document.querySelectorAll('.bg-blue-600.rounded-full');
    expect(unreadDots.length).toBeGreaterThan(0);
  });

  it('shows attachment indicators when present', () => {
    const threadsWithAttachments = [
      {
        ...mockThreads[0],
        isExpanded: true,
        messages: [
          {
            ...mockThreads[0].messages[0],
            hasAttachments: true,
            attachmentCount: 2
          }
        ]
      }
    ];
    
    render(EmailThread, { props: { threads: threadsWithAttachments } });
    
    expect(screen.getByText('2 attachments')).toBeInTheDocument();
  });

  it('generates correct avatar initials', () => {
    const expandedThreads = [
      {
        ...mockThreads[0],
        isExpanded: true
      }
    ];
    
    render(EmailThread, { props: { threads: expandedThreads } });
    
    // Check if initials are generated correctly
    expect(screen.getByText('JD')).toBeInTheDocument(); // John Doe
    expect(screen.getByText('JS')).toBeInTheDocument(); // Jane Smith
  });
});
