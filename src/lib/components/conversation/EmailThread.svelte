<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { formatMessageTime, formatMessageDate, truncateMessage } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import {
		ChevronDownOutline,
		ChevronRightOutline,
		MailBoxOutline,
		ClockOutline,
		ReplyOutline,
		ReplyAllOutline,
		ForwardOutline,
		StarOutline,
		ArchiveArrowDownOutline,
		TrashBinOutline,
		DotsHorizontalOutline,
		PlusOutline,
		BookmarkOutline
	} from 'flowbite-svelte-icons';

	// Email thread interface
	interface EmailMessage {
		id: number;
		threadId: string;
		sender: {
			name: string;
			email: string;
			avatar?: string;
		};
		subject: string;
		body: string;
		timestamp: string;
		isRead: boolean;
		isFromSelf: boolean;
		hasAttachments?: boolean;
		attachmentCount?: number;
	}

	interface EmailThread {
		id: string;
		subject: string;
		participants: string[];
		messageCount: number;
		lastActivity: string;
		isExpanded: boolean;
		messages: EmailMessage[];
		hasUnread: boolean;
		isNew?: boolean;
	}

	// Props
	export let threads: EmailThread[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();

	// Mock data for initial development - organized by different days
	const mockThreads: EmailThread[] = [
		// TODAY - New thread
		{
			id: 'thread-1',
			subject: 'New chilli piper licenses',
			participants: ['Samuel Jennings', 'You'],
			messageCount: 3,
			lastActivity: '2024-09-26T14:31:00Z',
			isExpanded: false,
			hasUnread: true,
			isNew: true,
			messages: [
				{
					id: 1,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'New chilli piper licenses',
					body: 'Hey Scott, have you orem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
					timestamp: '2024-09-25T20:31:00Z',
					isRead: true,
					isFromSelf: false
				},
				{
					id: 2,
					threadId: 'thread-1',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: New chilli piper licenses',
					body: 'Hey Samuel, have you orem ipsum dolor sit amet consectetur adipiscing elit. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
					timestamp: '2024-09-25T20:31:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 3,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Re: New chilli piper licenses',
					body: 'Hey Scott,\n\nWhen can we expect to deliver those new licences?\n\nBest,\nSamuel',
					timestamp: '2024-09-26T14:31:00Z',
					isRead: false,
					isFromSelf: false,
					hasAttachments: false
				}
			]
		},
		// TODAY - Another thread
		{
			id: 'thread-2',
			subject: 'Q4 Budget Review Meeting',
			participants: ['Finance Team', 'You', 'Manager'],
			messageCount: 4,
			lastActivity: '2024-09-26T11:45:00Z',
			isExpanded: false,
			hasUnread: true,
			messages: [
				{
					id: 4,
					threadId: 'thread-2',
					sender: {
						name: 'Finance Team',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Q4 Budget Review Meeting',
					body: 'Hi everyone, we need to schedule our Q4 budget review meeting. Please let me know your availability for next week.',
					timestamp: '2024-09-26T11:45:00Z',
					isRead: false,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				}
			]
		},
		// YESTERDAY
		{
			id: 'thread-3',
			subject: 'Weekly Team Standup Notes',
			participants: ['Team Lead', 'Development Team', 'You'],
			messageCount: 6,
			lastActivity: '2024-09-25T16:20:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 5,
					threadId: 'thread-3',
					sender: {
						name: 'Team Lead',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Weekly Team Standup Notes',
					body: 'Hi team, here are the notes from our weekly standup meeting. Please review and let me know if I missed anything important.',
					timestamp: '2024-09-25T16:20:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 3
				}
			]
		},
		// YESTERDAY
		{
			id: 'thread-4',
			subject: 'Client Feedback on Latest Prototype',
			participants: ['Client Services', 'You', 'Design Team'],
			messageCount: 8,
			lastActivity: '2024-09-25T14:15:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 6,
					threadId: 'thread-4',
					sender: {
						name: 'Client Services',
						email: '<EMAIL>'
					},
					subject: 'Client Feedback on Latest Prototype',
					body: 'The client has provided detailed feedback on the latest prototype. Overall they are very pleased with the direction, but have some specific requests for modifications.',
					timestamp: '2024-09-25T14:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				}
			]
		},
		// EARLIER THIS WEEK
		{
			id: 'thread-5',
			subject: 'Security Update Required - Action Needed',
			participants: ['IT Security', 'All Staff', 'You'],
			messageCount: 3,
			lastActivity: '2024-09-23T09:30:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 7,
					threadId: 'thread-5',
					sender: {
						name: 'IT Security',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Security Update Required - Action Needed',
					body: 'Important: Please update your system passwords and enable two-factor authentication by end of week. This is mandatory for all staff members.',
					timestamp: '2024-09-23T09:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				}
			]
		},
		// LAST WEEK
		{
			id: 'thread-6',
			subject: 'Holiday Schedule Planning',
			participants: ['HR Department', 'You', 'Team Managers'],
			messageCount: 12,
			lastActivity: '2024-09-20T15:45:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 8,
					threadId: 'thread-6',
					sender: {
						name: 'HR Department',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Holiday Schedule Planning',
					body: 'Please submit your holiday requests for the upcoming quarter. We need to ensure adequate coverage across all departments.',
					timestamp: '2024-09-20T15:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				}
			]
		}
	];

	// Initialize with mock data if no threads provided
	if (threads.length === 0) {
		threads = mockThreads;
	}

	// Toggle thread expansion
	function toggleThread(threadId: string) {
		threads = threads.map(thread => {
			if (thread.id === threadId) {
				return { ...thread, isExpanded: !thread.isExpanded };
			}
			return thread;
		});
		
		dispatch('threadToggle', { threadId, isExpanded: threads.find(t => t.id === threadId)?.isExpanded });
	}

	// Handle email click
	function handleEmailClick(email: EmailMessage) {
		dispatch('emailClick', { email });
	}

	// Handle email actions
	function handleEmailAction(action: string, email: EmailMessage, event?: Event) {
		if (event) {
			event.stopPropagation();
		}
		dispatch('emailAction', { action, email });
	}

	// Group threads by date for display
	function groupThreadsByDate(threads: EmailThread[]) {
		const today = new Date();
		const yesterday = new Date(today);
		yesterday.setDate(yesterday.getDate() - 1);

		const thisWeekStart = new Date(today);
		thisWeekStart.setDate(today.getDate() - today.getDay());

		const groups = {
			today: [] as EmailThread[],
			yesterday: [] as EmailThread[],
			thisWeek: [] as EmailThread[],
			older: [] as EmailThread[]
		};

		threads.forEach(thread => {
			const threadDate = new Date(thread.lastActivity);
			const threadDateStr = threadDate.toDateString();

			if (threadDateStr === today.toDateString()) {
				groups.today.push(thread);
			} else if (threadDateStr === yesterday.toDateString()) {
				groups.yesterday.push(thread);
			} else if (threadDate >= thisWeekStart) {
				groups.thisWeek.push(thread);
			} else {
				groups.older.push(thread);
			}
		});

		return groups;
	}

	// Get avatar color for sender
	function getAvatarColor(name: string): string {
		const colors = [
			'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
			'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
		];
		
		let hash = 0;
		for (let i = 0; i < name.length; i++) {
			hash = name.charCodeAt(i) + ((hash << 5) - hash);
		}
		
		return colors[Math.abs(hash) % colors.length];
	}

	// Format email body with line breaks
	function formatEmailBody(body: string): string {
		if (!body) return '';
		return body.replace(/\n/g, '<br>');
	}

	// Get relative time for last activity
	function getRelativeTime(timestamp: string): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffHours / 24);

		if (diffHours < 1) return 'Just now';
		if (diffHours < 24) return `${diffHours}h ago`;
		if (diffDays === 1) return 'Yesterday';
		if (diffDays < 7) return `${diffDays}d ago`;

		return formatMessageDate(timestamp);
	}

	// Reactive statement to group threads by date
	$: groupedThreads = groupThreadsByDate(threads);
</script>

<div class="email-thread-container flex-1 overflow-y-auto bg-gray-50 px-6 py-4">
	{#if loading}
		<div class="flex h-full items-center justify-center">
			<div class="flex items-center space-x-2">
				<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				<span class="text-gray-500">Loading email threads...</span>
			</div>
		</div>
	{:else if threads.length === 0}
		<div class="flex h-full items-center justify-center">
			<div class="text-center text-gray-500">
				<MailBoxOutline class="mx-auto h-12 w-12 mb-4 text-gray-400" />
				<p>No email threads found</p>
			</div>
		</div>
	{:else}
		<!-- Today's threads -->
		{#if groupedThreads.today.length > 0}
			<div class="mb-6">
				<div class="flex items-center justify-center mb-4">
					<span class="text-sm font-medium text-gray-500 uppercase tracking-wide">
						Today
					</span>
				</div>
				{#each groupedThreads.today as thread (thread.id)}
					<div class="email-thread mb-4 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
						<!-- Thread header -->
						<div
							class="thread-header p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-150"
							on:click={() => toggleThread(thread.id)}
							on:keydown={(e) => e.key === 'Enter' && toggleThread(thread.id)}
							role="button"
							tabindex="0"
						>
							<div class="flex items-start justify-between">
								<div class="flex items-start space-x-3 flex-1 min-w-0">
									<!-- Expand/collapse icon -->
									<div class="flex-shrink-0 mt-1">
										{#if thread.isExpanded}
											<ChevronDownOutline class="h-4 w-4 text-gray-400" />
										{:else}
											<ChevronRightOutline class="h-4 w-4 text-gray-400" />
										{/if}
									</div>

									<!-- Thread info -->
									<div class="flex-1 min-w-0">
										<div class="flex items-center space-x-2 mb-1">
											{#if thread.isNew}
												<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
													NEW
												</span>
											{/if}
											<h3 class="text-sm font-medium text-gray-900 truncate">
												{thread.subject}
											</h3>
										</div>

										<div class="flex items-center space-x-4 text-xs text-gray-500">
											<span>{thread.participants.join(', ')}</span>
											<span>•</span>
											<span>{thread.messageCount} message{thread.messageCount !== 1 ? 's' : ''}</span>
											<span>•</span>
											<span>{getRelativeTime(thread.lastActivity)}</span>
										</div>
									</div>
								</div>

								<!-- Unread indicator -->
								{#if thread.hasUnread}
									<div class="flex-shrink-0 ml-2">
										<div class="h-2 w-2 bg-blue-600 rounded-full"></div>
									</div>
								{/if}
							</div>
						</div>

						<!-- Expanded thread messages -->
						{#if thread.isExpanded}
							<div class="thread-messages border-t border-gray-100">
								{#each thread.messages as message, index (message.id)}
									<div class="message-item border-b border-gray-100 last:border-b-0">
										<div
											class="p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
											on:click={() => handleEmailClick(message)}
											on:keydown={(e) => e.key === 'Enter' && handleEmailClick(message)}
											role="button"
											tabindex="0"
										>
											<div class="flex items-start space-x-3">
												<!-- Avatar -->
												<div class="flex-shrink-0">
													{#if message.sender.avatar}
														<img
															src={message.sender.avatar}
															alt={message.sender.name}
															class="h-8 w-8 rounded-full object-cover"
														/>
													{:else}
														<div class="h-8 w-8 rounded-full {getAvatarColor(message.sender.name)} flex items-center justify-center">
															<span class="text-xs font-medium text-white">
																{getInitials(message.sender.name)}
															</span>
														</div>
													{/if}
												</div>

												<!-- Message content -->
												<div class="flex-1 min-w-0">
													<div class="flex items-center justify-between mb-1">
														<div class="flex items-center space-x-2">
															<span class="text-sm font-medium text-gray-900">
																{message.sender.name}
															</span>
															{#if !message.isRead}
																<div class="h-1.5 w-1.5 bg-blue-600 rounded-full"></div>
															{/if}
														</div>
														<div class="flex items-center space-x-2 text-xs text-gray-500">
															<ClockOutline class="h-3 w-3" />
															<span>{formatMessageTime(message.timestamp)}</span>
														</div>
													</div>

													<p class="text-sm text-gray-600 line-clamp-2 mb-3">
														{@html formatEmailBody(truncateMessage(message.body, 150))}
													</p>

													{#if message.hasAttachments}
														<div class="flex items-center space-x-1 text-xs text-gray-500 mb-3">
															<svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
															</svg>
															<span>{message.attachmentCount} attachment{message.attachmentCount !== 1 ? 's' : ''}</span>
														</div>
													{/if}
												</div>
											</div>
										</div>

										<!-- Email action buttons -->
										<div class="px-4 pb-4">
											<div class="flex items-center space-x-2">
												<button
													class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('reply', message, e)}
												>
													<ReplyOutline class="h-3 w-3 mr-1" />
													Reply
												</button>
												<button
													class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('replyAll', message, e)}
												>
													<ReplyAllOutline class="h-3 w-3 mr-1" />
													Reply All
												</button>
												<button
													class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('forward', message, e)}
												>
													<ForwardOutline class="h-3 w-3 mr-1" />
													Forward
												</button>
												<button
													class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('addTask', message, e)}
												>
													<PlusOutline class="h-3 w-3 mr-1" />
													Task
												</button>
												<button
													class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('addToCadence', message, e)}
												>
													<BookmarkOutline class="h-3 w-3 mr-1" />
													Add to Cadence
												</button>
												<button
													class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('instantBooker', message, e)}
												>
													<ClockOutline class="h-3 w-3 mr-1" />
													Instant Booker
												</button>
												<button
													class="inline-flex items-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
													on:click={(e) => handleEmailAction('more', message, e)}
												>
													<DotsHorizontalOutline class="h-3 w-3" />
												</button>
											</div>
										</div>
									</div>
								{/each}
							</div>
						{/if}
					</div>
				{/each}
			</div>
		{/if}
</div>

<style>
	.email-thread-container {
		/* Custom scrollbar styling */
		scrollbar-width: thin;
		scrollbar-color: #cbd5e0 #f7fafc;
	}

	.email-thread-container::-webkit-scrollbar {
		width: 6px;
	}

	.email-thread-container::-webkit-scrollbar-track {
		background: #f7fafc;
	}

	.email-thread-container::-webkit-scrollbar-thumb {
		background-color: #cbd5e0;
		border-radius: 3px;
	}

	.email-thread-container::-webkit-scrollbar-thumb:hover {
		background-color: #a0aec0;
	}

	/* Line clamp utility for text truncation */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Smooth transitions */
	.thread-header {
		transition: background-color 0.15s ease-in-out;
	}

	.message-item {
		transition: background-color 0.15s ease-in-out;
	}

	/* Focus styles for accessibility */
	.thread-header:focus,
	.message-item:focus {
		outline: 2px solid #3b82f6;
		outline-offset: -2px;
	}
</style>
