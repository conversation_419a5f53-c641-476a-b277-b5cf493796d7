<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { formatMessageTime, formatMessageDate, truncateMessage } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import { ChevronDownOutline, ChevronRightOutline, MailBoxOutline, ClockOutline } from 'flowbite-svelte-icons';

	// Email thread interface
	interface EmailMessage {
		id: number;
		threadId: string;
		sender: {
			name: string;
			email: string;
			avatar?: string;
		};
		subject: string;
		body: string;
		timestamp: string;
		isRead: boolean;
		isFromSelf: boolean;
		hasAttachments?: boolean;
		attachmentCount?: number;
	}

	interface EmailThread {
		id: string;
		subject: string;
		participants: string[];
		messageCount: number;
		lastActivity: string;
		isExpanded: boolean;
		messages: EmailMessage[];
		hasUnread: boolean;
		isNew?: boolean;
	}

	// Props
	export let threads: EmailThread[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();

	// Mock data for initial development
	const mockThreads: EmailThread[] = [
		{
			id: 'thread-1',
			subject: 'New chilli piper licenses',
			participants: ['Samuel Jennings', 'You'],
			messageCount: 3,
			lastActivity: '2024-09-26T14:31:00Z',
			isExpanded: false,
			hasUnread: true,
			isNew: true,
			messages: [
				{
					id: 1,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'New chilli piper licenses',
					body: 'Hey Scott, have you orem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
					timestamp: '2024-09-25T20:31:00Z',
					isRead: true,
					isFromSelf: false
				},
				{
					id: 2,
					threadId: 'thread-1',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: New chilli piper licenses',
					body: 'Hey Samuel, have you orem ipsum dolor sit amet consectetur adipiscing elit. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
					timestamp: '2024-09-25T20:31:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 3,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Re: New chilli piper licenses',
					body: 'Hey Scott,\n\nWhen can we expect to deliver those new licences?\n\nBest,\nSamuel',
					timestamp: '2024-09-26T14:31:00Z',
					isRead: false,
					isFromSelf: false,
					hasAttachments: false
				}
			]
		},
		{
			id: 'thread-2',
			subject: 'Project timeline update',
			participants: ['Alice Johnson', 'Bob Smith', 'You'],
			messageCount: 5,
			lastActivity: '2024-09-26T10:15:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 4,
					threadId: 'thread-2',
					sender: {
						name: 'Alice Johnson',
						email: '<EMAIL>',
						avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face'
					},
					subject: 'Project timeline update',
					body: 'Hi team, I wanted to provide an update on our current project timeline. We are making good progress but need to adjust some milestones.',
					timestamp: '2024-09-26T10:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				}
			]
		}
	];

	// Initialize with mock data if no threads provided
	if (threads.length === 0) {
		threads = mockThreads;
	}

	// Toggle thread expansion
	function toggleThread(threadId: string) {
		threads = threads.map(thread => {
			if (thread.id === threadId) {
				return { ...thread, isExpanded: !thread.isExpanded };
			}
			return thread;
		});
		
		dispatch('threadToggle', { threadId, isExpanded: threads.find(t => t.id === threadId)?.isExpanded });
	}

	// Handle email click
	function handleEmailClick(email: EmailMessage) {
		dispatch('emailClick', { email });
	}

	// Get avatar color for sender
	function getAvatarColor(name: string): string {
		const colors = [
			'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
			'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
		];
		
		let hash = 0;
		for (let i = 0; i < name.length; i++) {
			hash = name.charCodeAt(i) + ((hash << 5) - hash);
		}
		
		return colors[Math.abs(hash) % colors.length];
	}

	// Format email body with line breaks
	function formatEmailBody(body: string): string {
		if (!body) return '';
		return body.replace(/\n/g, '<br>');
	}

	// Get relative time for last activity
	function getRelativeTime(timestamp: string): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffHours / 24);

		if (diffHours < 1) return 'Just now';
		if (diffHours < 24) return `${diffHours}h ago`;
		if (diffDays === 1) return 'Yesterday';
		if (diffDays < 7) return `${diffDays}d ago`;
		
		return formatMessageDate(timestamp);
	}
</script>

<div class="email-thread-container flex-1 overflow-y-auto bg-gray-50 px-6 py-4">
	{#if loading}
		<div class="flex h-full items-center justify-center">
			<div class="flex items-center space-x-2">
				<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				<span class="text-gray-500">Loading email threads...</span>
			</div>
		</div>
	{:else if threads.length === 0}
		<div class="flex h-full items-center justify-center">
			<div class="text-center text-gray-500">
				<MailBoxOutline class="mx-auto h-12 w-12 mb-4 text-gray-400" />
				<p>No email threads found</p>
			</div>
		</div>
	{:else}
		<!-- Date header -->
		<div class="mb-6">
			<div class="flex items-center justify-center">
				<span class="text-sm font-medium text-gray-500 uppercase tracking-wide">
					Today
				</span>
			</div>
		</div>

		<!-- Email threads -->
		{#each threads as thread (thread.id)}
			<div class="email-thread mb-4 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
				<!-- Thread header -->
				<div 
					class="thread-header p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-150"
					on:click={() => toggleThread(thread.id)}
					on:keydown={(e) => e.key === 'Enter' && toggleThread(thread.id)}
					role="button"
					tabindex="0"
				>
					<div class="flex items-start justify-between">
						<div class="flex items-start space-x-3 flex-1 min-w-0">
							<!-- Expand/collapse icon -->
							<div class="flex-shrink-0 mt-1">
								{#if thread.isExpanded}
									<ChevronDownOutline class="h-4 w-4 text-gray-400" />
								{:else}
									<ChevronRightOutline class="h-4 w-4 text-gray-400" />
								{/if}
							</div>

							<!-- Thread info -->
							<div class="flex-1 min-w-0">
								<div class="flex items-center space-x-2 mb-1">
									{#if thread.isNew}
										<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
											NEW
										</span>
									{/if}
									<h3 class="text-sm font-medium text-gray-900 truncate">
										{thread.subject}
									</h3>
								</div>
								
								<div class="flex items-center space-x-4 text-xs text-gray-500">
									<span>{thread.participants.join(', ')}</span>
									<span>•</span>
									<span>{thread.messageCount} message{thread.messageCount !== 1 ? 's' : ''}</span>
									<span>•</span>
									<span>{getRelativeTime(thread.lastActivity)}</span>
								</div>
							</div>
						</div>

						<!-- Unread indicator -->
						{#if thread.hasUnread}
							<div class="flex-shrink-0 ml-2">
								<div class="h-2 w-2 bg-blue-600 rounded-full"></div>
							</div>
						{/if}
					</div>
				</div>

				<!-- Expanded thread messages -->
				{#if thread.isExpanded}
					<div class="thread-messages border-t border-gray-100">
						{#each thread.messages as message, index (message.id)}
							<div 
								class="message-item p-4 {index < thread.messages.length - 1 ? 'border-b border-gray-100' : ''} hover:bg-gray-50 cursor-pointer transition-colors duration-150"
								on:click={() => handleEmailClick(message)}
								on:keydown={(e) => e.key === 'Enter' && handleEmailClick(message)}
								role="button"
								tabindex="0"
							>
								<div class="flex items-start space-x-3">
									<!-- Avatar -->
									<div class="flex-shrink-0">
										{#if message.sender.avatar}
											<img 
												src={message.sender.avatar} 
												alt={message.sender.name}
												class="h-8 w-8 rounded-full object-cover"
											/>
										{:else}
											<div class="h-8 w-8 rounded-full {getAvatarColor(message.sender.name)} flex items-center justify-center">
												<span class="text-xs font-medium text-white">
													{getInitials(message.sender.name)}
												</span>
											</div>
										{/if}
									</div>

									<!-- Message content -->
									<div class="flex-1 min-w-0">
										<div class="flex items-center justify-between mb-1">
											<div class="flex items-center space-x-2">
												<span class="text-sm font-medium text-gray-900">
													{message.sender.name}
												</span>
												{#if !message.isRead}
													<div class="h-1.5 w-1.5 bg-blue-600 rounded-full"></div>
												{/if}
											</div>
											<div class="flex items-center space-x-2 text-xs text-gray-500">
												<ClockOutline class="h-3 w-3" />
												<span>{formatMessageTime(message.timestamp)}</span>
											</div>
										</div>
										
										<p class="text-sm text-gray-600 line-clamp-2 mb-2">
											{@html formatEmailBody(truncateMessage(message.body, 150))}
										</p>

										{#if message.hasAttachments}
											<div class="flex items-center space-x-1 text-xs text-gray-500">
												<svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
												</svg>
												<span>{message.attachmentCount} attachment{message.attachmentCount !== 1 ? 's' : ''}</span>
											</div>
										{/if}
									</div>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>
		{/each}
	{/if}
</div>

<style>
	.email-thread-container {
		/* Custom scrollbar styling */
		scrollbar-width: thin;
		scrollbar-color: #cbd5e0 #f7fafc;
	}

	.email-thread-container::-webkit-scrollbar {
		width: 6px;
	}

	.email-thread-container::-webkit-scrollbar-track {
		background: #f7fafc;
	}

	.email-thread-container::-webkit-scrollbar-thumb {
		background-color: #cbd5e0;
		border-radius: 3px;
	}

	.email-thread-container::-webkit-scrollbar-thumb:hover {
		background-color: #a0aec0;
	}

	/* Line clamp utility for text truncation */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Smooth transitions */
	.thread-header {
		transition: background-color 0.15s ease-in-out;
	}

	.message-item {
		transition: background-color 0.15s ease-in-out;
	}

	/* Focus styles for accessibility */
	.thread-header:focus,
	.message-item:focus {
		outline: 2px solid #3b82f6;
		outline-offset: -2px;
	}
</style>
